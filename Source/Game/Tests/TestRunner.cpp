// Copyright Epic Games, Inc. All Rights Reserved.

#include "TestRunner.h"
#include "Performance/PerformanceOptimizer.h"
#include "Performance/CacheManager.h"
#include "Performance/GPUComputeManager.h"
#include "Performance/ObjectPoolManager.h"
#include "LevelGen/MapUtil.h"
#include "Engine/World.h"
#include "HAL/PlatformMemory.h"
#include "Async/Async.h"
#include "HAL/PlatformProcess.h"
#include "Math/UnrealMathUtility.h"
#include "LevelGen/MapUtil.h"

UTestRunner* UTestRunner::Get(const UWorld* World)
{
    if (LIKELY(World))
    {
        return World->GetSubsystem<UTestRunner>();
    }
    return nullptr;
}

void UTestRunner::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogTemp, Log, TEXT("【测试运行器】初始化开始"));
    
    // 初始化测试环境
    if (!InitializeTestEnvironment())
    {
        UE_LOG(LogTemp, Error, TEXT("【测试运行器】测试环境初始化失败"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("【测试运行器】初始化完成"));
}

void UTestRunner::Deinitialize()
{
    // 停止当前测试
    StopCurrentTests();
    
    // 清理测试环境
    CleanupTestEnvironment();
    
    UE_LOG(LogTemp, Log, TEXT("【测试运行器】反初始化完成"));
    
    Super::Deinitialize();
}

bool UTestRunner::ShouldCreateSubsystem(UObject* Outer) const
{
    // 只在游戏世界中创建测试运行器
    if (const UWorld* World = Cast<UWorld>(Outer))
    {
        return World->IsGameWorld();
    }
    return false;
}

bool UTestRunner::RunTestSuite(const FTestSuiteConfig& Config)
{
    if (bIsTestRunning)
    {
        UE_LOG(LogTemp, Warning, TEXT("【测试运行器】测试已在运行中，无法启动新测试"));
        return false;
    }
    
    if (!ValidateTestPreconditions(Config))
    {
        UE_LOG(LogTemp, Error, TEXT("【测试运行器】测试前置条件验证失败"));
        return false;
    }
    
    CurrentConfig = Config;
    bIsTestRunning = true;
    CurrentTestProgress = 0.0f;
    LastTestResults.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始运行测试套件"));
    
    // 异步执行测试
    AsyncTask(ENamedThreads::GameThread, [this, Config]()
    {
        TArray<FTestResultDetail> AllResults;
        
        // 执行各类测试
        TArray<FTestResultDetail> PerfOptimizerResults = RunPerformanceOptimizerTests(Config);
        AllResults.Append(PerfOptimizerResults);

        TArray<FTestResultDetail> CacheResults = RunCacheManagerTests(Config);
        AllResults.Append(CacheResults);

        TArray<FTestResultDetail> GPUResults = RunGPUComputeManagerTests(Config);
        AllResults.Append(GPUResults);

        TArray<FTestResultDetail> ObjectPoolResults = RunObjectPoolManagerTests(Config);
        AllResults.Append(ObjectPoolResults);

        // 如果是基准测试模式，执行基准测试
        if (Config.TestMode == ETestMode::Benchmark)
        {
            TArray<FTestResultDetail> BenchmarkResults = RunBenchmarkTests(Config);
            AllResults.Append(BenchmarkResults);
        }
        
        // 保存结果
        LastTestResults = AllResults;
        bIsTestRunning = false;
        CurrentTestProgress = 1.0f;
        
        // 通知测试完成
        NotifyTestSuiteCompleted(AllResults, Config);
        
        UE_LOG(LogTemp, Log, TEXT("【测试运行器】测试套件执行完成，总计 %d 个测试"), AllResults.Num());
    });
    
    return true;
}

bool UTestRunner::RunTestCategory(const FString& TestCategory, const FTestSuiteConfig& Config)
{
    if (bIsTestRunning)
    {
        UE_LOG(LogTemp, Warning, TEXT("【测试运行器】测试已在运行中"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("【测试运行器】运行测试类别: %s"), *TestCategory);
    
    // 根据类别运行特定测试
    if (TestCategory == TEXT("PerformanceOptimizer"))
    {
        return RunTestSuite(Config);
    }
    else if (TestCategory == TEXT("CacheManager"))
    {
        return RunTestSuite(Config);
    }
    else if (TestCategory == TEXT("GPUComputeManager"))
    {
        return RunTestSuite(Config);
    }
    else if (TestCategory == TEXT("ObjectPoolManager"))
    {
        return RunTestSuite(Config);
    }
    
    UE_LOG(LogTemp, Warning, TEXT("【测试运行器】未知的测试类别: %s"), *TestCategory);
    return false;
}

bool UTestRunner::RunPerformanceBenchmarks(const FTestSuiteConfig& Config)
{
    FTestSuiteConfig BenchmarkConfig = Config;
    BenchmarkConfig.TestMode = ETestMode::Benchmark;
    return RunTestSuite(BenchmarkConfig);
}

void UTestRunner::StopCurrentTests()
{
    if (bIsTestRunning)
    {
        bIsTestRunning = false;
        CurrentTestProgress = 0.0f;
        UE_LOG(LogTemp, Log, TEXT("【测试运行器】测试已停止"));
    }
}

bool UTestRunner::IsTestRunning() const
{
    return bIsTestRunning;
}

float UTestRunner::GetTestProgress() const
{
    return CurrentTestProgress;
}

TArray<FTestResultDetail> UTestRunner::GetLastTestResults() const
{
    return LastTestResults;
}

FString UTestRunner::GenerateTestReport(const TArray<FTestResultDetail>& Results, const FTestSuiteConfig& Config) const
{
    FString Report;
    Report += TEXT("=== 性能优化测试报告 ===\n\n");
    
    // 统计信息
    int32 PassedTests = 0;
    int32 FailedTests = 0;
    float TotalExecutionTime = 0.0f;
    float TotalMemoryUsage = 0.0f;
    
    for (const FTestResultDetail& Result : Results)
    {
        if (Result.Status == ETestResultStatus::Passed)
        {
            PassedTests++;
        }
        else
        {
            FailedTests++;
        }
        TotalExecutionTime += Result.ExecutionTimeMs;
        TotalMemoryUsage += Result.MemoryUsageMB;
    }
    
    Report += FString::Printf(TEXT("总测试数: %d\n"), Results.Num());
    Report += FString::Printf(TEXT("通过: %d\n"), PassedTests);
    Report += FString::Printf(TEXT("失败: %d\n"), FailedTests);
    Report += FString::Printf(TEXT("总执行时间: %.2f ms\n"), TotalExecutionTime);
    Report += FString::Printf(TEXT("总内存使用: %.2f MB\n\n"), TotalMemoryUsage);
    
    // 详细结果
    Report += TEXT("=== 详细结果 ===\n");
    for (const FTestResultDetail& Result : Results)
    {
        FString StatusText = (Result.Status == ETestResultStatus::Passed) ? TEXT("PASS") : TEXT("FAIL");
        Report += FString::Printf(TEXT("[%s] %s - 执行时间: %.2f ms, 内存: %.2f MB\n"),
                                 *StatusText, *Result.TestName, Result.ExecutionTimeMs, Result.MemoryUsageMB);
        
        if (!Result.ErrorMessage.IsEmpty())
        {
            Report += FString::Printf(TEXT("  错误: %s\n"), *Result.ErrorMessage);
        }
    }
    
    return Report;
}

bool UTestRunner::SaveTestResults(const TArray<FTestResultDetail>& Results, const FString& FilePath) const
{
    FString Report = GenerateTestReport(Results, CurrentConfig);
    return FFileHelper::SaveStringToFile(Report, *FilePath);
}

void UTestRunner::SetTestConfig(const FTestSuiteConfig& NewConfig)
{
    CurrentConfig = NewConfig;
}

FTestSuiteConfig UTestRunner::GetTestConfig() const
{
    return CurrentConfig;
}

// ========== 私有测试执行方法实现 ==========

TArray<FTestResultDetail> UTestRunner::RunPerformanceOptimizerTests(const FTestSuiteConfig& Config)
{
    TArray<FTestResultDetail> Results;

    if (!IsValid(PerformanceOptimizer))
    {
        return Results;
    }

    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始执行性能优化器测试"));

    // 测试1：基本功能测试
    {
        FString TestName = TEXT("PerformanceOptimizer_BasicFunctionality");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 测试基本初始化和功能
            if (!IsValid(PerformanceOptimizer))
            {
                bTestPassed = false;
                ErrorMessage = TEXT("PerformanceOptimizer not valid");
                return;
            }

            if (!PerformanceOptimizer->IsInitialized())
            {
                bTestPassed = false;
                ErrorMessage = TEXT("PerformanceOptimizer not initialized");
                return;
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    // 测试2：缓存数据测试
    {
        FString TestName = TEXT("PerformanceOptimizer_CacheDataTest");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 创建测试数据
            TArray<FMapCell> TestData;
            for (int32 i = 0; i < 10; ++i)
            {
                FMapCell Cell;
                Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
                Cell.ZLevel = static_cast<float>(i);
                TestData.Add(Cell);
            }

            // 测试缓存数据功能
            FString CacheKey = TEXT("TestCacheKey");
            PerformanceOptimizer->CacheMapData(CacheKey, TestData);

            // 尝试获取缓存数据
            TArray<FMapCell> RetrievedData;
            bool bCacheHit = PerformanceOptimizer->GetCachedMapData(CacheKey, RetrievedData);

            if (!bCacheHit)
            {
                bTestPassed = false;
                ErrorMessage = TEXT("Failed to retrieve cached data");
                return;
            }

            if (RetrievedData.Num() != TestData.Num())
            {
                bTestPassed = false;
                ErrorMessage = TEXT("Retrieved data size mismatch");
                return;
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    return Results;
}

TArray<FTestResultDetail> UTestRunner::RunCacheManagerTests(const FTestSuiteConfig& Config)
{
    TArray<FTestResultDetail> Results;

    if (!IsValid(CacheManager))
    {
        return Results;
    }

    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始执行缓存管理器测试"));

    // 测试1：基本缓存功能
    {
        FString TestName = TEXT("CacheManager_BasicCaching");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 创建测试数据
            TArray<FMapCell> TestData;
            for (int32 i = 0; i < 5; ++i)
            {
                FMapCell Cell;
                Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
                Cell.ZLevel = static_cast<float>(i);
                TestData.Add(Cell);
            }

            // 测试L1缓存
            FString CacheKey = TEXT("CacheManager_L1_Test");
            CacheManager->CacheData(CacheKey, TestData, ECacheLevel::L1_Memory);

            TArray<FMapCell> RetrievedData;
            bool bHit = CacheManager->GetCachedData(CacheKey, RetrievedData);

            if (!bHit)
            {
                bTestPassed = false;
                ErrorMessage = TEXT("L1 cache miss");
                return;
            }

            if (RetrievedData.Num() != TestData.Num())
            {
                bTestPassed = false;
                ErrorMessage = TEXT("L1 cache data size mismatch");
                return;
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    // 测试2：缓存统计测试
    {
        FString TestName = TEXT("CacheManager_StatisticsTest");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 获取缓存统计信息
            FCacheStats Stats = CacheManager->GetCacheStats();

            // 验证统计数据的合理性
            if (Stats.L1Hits < 0 || Stats.L2Hits < 0 || Stats.L3Hits < 0 || Stats.Misses < 0)
            {
                bTestPassed = false;
                ErrorMessage = TEXT("Invalid cache statistics");
                return;
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    return Results;
}

TArray<FTestResultDetail> UTestRunner::RunGPUComputeManagerTests(const FTestSuiteConfig& Config)
{
    TArray<FTestResultDetail> Results;

    if (!IsValid(GPUComputeManager))
    {
        return Results;
    }

    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始执行GPU计算管理器测试"));

    // 测试1：GPU可用性测试
    {
        FString TestName = TEXT("GPUComputeManager_GPUAvailability");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 检查GPU计算是否可用
            bool bGPUAvailable = GPUComputeManager->IsGPUComputeAvailable();

            UE_LOG(LogTemp, Log, TEXT("【GPU测试】GPU计算可用性: %s"),
                   bGPUAvailable ? TEXT("可用") : TEXT("不可用"));

            // 这个测试总是通过，只是记录GPU状态
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    // 测试2：距离计算测试
    {
        FString TestName = TEXT("GPUComputeManager_DistanceCalculation");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 创建测试点数据
            TArray<FIntPoint> PointsA, PointsB;
            for (int32 i = 0; i < 5; ++i)
            {
                PointsA.Add(FIntPoint(0, 0));
                PointsB.Add(FIntPoint(i * 10, 0));
            }

            TArray<float> OutDistances;
            int32 TaskID = GPUComputeManager->CalculateDistancesBatch_GPU(PointsA, PointsB, OutDistances);

            if (TaskID < 0)
            {
                bTestPassed = false;
                ErrorMessage = TEXT("Failed to submit GPU distance calculation task");
                return;
            }

            // 等待任务完成（简单等待）
            int32 WaitCount = 0;
            while (GPUComputeManager->GetTaskInfo(TaskID).Status == EGPUTaskStatus::Executing && WaitCount < 100)
            {
                FPlatformProcess::Sleep(0.01f);
                WaitCount++;
            }

            EGPUTaskStatus FinalStatus = GPUComputeManager->GetTaskInfo(TaskID).Status;
            if (FinalStatus != EGPUTaskStatus::Completed && FinalStatus != EGPUTaskStatus::Failed)
            {
                bTestPassed = false;
                ErrorMessage = TEXT("GPU task did not complete in time");
                return;
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    return Results;
}

TArray<FTestResultDetail> UTestRunner::RunObjectPoolManagerTests(const FTestSuiteConfig& Config)
{
    TArray<FTestResultDetail> Results;

    if (!IsValid(ObjectPoolManager))
    {
        return Results;
    }

    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始执行对象池管理器测试"));

    // 测试1：基本对象池功能
    {
        FString TestName = TEXT("ObjectPoolManager_BasicPooling");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 测试对象获取和归还
            TArray<TSharedPtr<FMapCell, ESPMode::ThreadSafe>> PooledObjects;
            for (int32 i = 0; i < 5; ++i)
            {
                TSharedPtr<FMapCell, ESPMode::ThreadSafe> Cell = ObjectPoolManager->GetPooledObject<FMapCell>();
                if (!Cell.IsValid())
                {
                    bTestPassed = false;
                    ErrorMessage = TEXT("Failed to get pooled object");
                    return;
                }
                PooledObjects.Add(Cell);
            }

            // 归还对象
            for (auto& Cell : PooledObjects)
            {
                ObjectPoolManager->ReturnPooledObject<FMapCell>(Cell);
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    return Results;
}

TArray<FTestResultDetail> UTestRunner::RunBenchmarkTests(const FTestSuiteConfig& Config)
{
    TArray<FTestResultDetail> Results;

    UE_LOG(LogTemp, Log, TEXT("【测试运行器】开始执行基准测试"));

    // 基准测试1：地图生成性能
    {
        FString TestName = TEXT("Benchmark_MapGeneration");
        float StartMemory = GetCurrentMemoryUsageMB();

        bool bTestPassed = true;
        FString ErrorMessage;

        double ExecutionTime = MeasureExecutionTime([&]()
        {
            // 测试地图生成性能
            if (IsValid(PerformanceOptimizer))
            {
                // 创建大量测试数据
                TArray<FMapCell> TestData;
                for (int32 i = 0; i < 1000; ++i)
                {
                    FMapCell Cell;
                    Cell.SurfaceCoverType = ESurfaceCoverType::Grass;
                    Cell.ZLevel = FMath::FRandRange(0.0f, 100.0f);
                    TestData.Add(Cell);
                }

                // 测试缓存性能
                FString CacheKey = FString::Printf(TEXT("Benchmark_Cache_%d"), FMath::Rand());
                PerformanceOptimizer->CacheMapData(CacheKey, TestData);

                TArray<FMapCell> RetrievedData;
                bool bCacheHit = PerformanceOptimizer->GetCachedMapData(CacheKey, RetrievedData);

                if (!bCacheHit)
                {
                    bTestPassed = false;
                    ErrorMessage = TEXT("Benchmark cache test failed");
                    return;
                }
            }
        });

        float EndMemory = GetCurrentMemoryUsageMB();

        ETestResultStatus Status = bTestPassed ? ETestResultStatus::Passed : ETestResultStatus::Failed;
        FTestResultDetail Result = CreateTestResult(
            TestName, Status,
            static_cast<float>(ExecutionTime), EndMemory - StartMemory,
            ErrorMessage
        );
        Results.Add(Result);
        NotifyTestCompleted(TestName, Result);
    }

    return Results;
}

// ========== 私有辅助方法实现 ==========

FTestResultDetail UTestRunner::CreateTestResult(
    const FString& TestName,
    ETestResultStatus Status,
    float ExecutionTime,
    float MemoryUsage,
    const FString& ErrorMessage)
{
    FTestResultDetail Result;
    Result.TestName = TestName;
    Result.Status = Status;
    Result.ExecutionTimeMs = ExecutionTime;
    Result.MemoryUsageMB = MemoryUsage;
    Result.ErrorMessage = ErrorMessage;
    Result.StartTime = FDateTime::Now();
    return Result;
}

template<typename FunctionType>
double UTestRunner::MeasureExecutionTime(FunctionType TestFunction)
{
    const double StartTime = FPlatformTime::Seconds();
    TestFunction();
    const double EndTime = FPlatformTime::Seconds();
    return (EndTime - StartTime) * 1000.0; // 转换为毫秒
}

float UTestRunner::GetCurrentMemoryUsageMB()
{
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    return static_cast<float>(MemStats.UsedPhysical) / (1024.0f * 1024.0f);
}

void UTestRunner::UpdateTestProgress(int32 CompletedTests, int32 TotalTests)
{
    CurrentTestCount = CompletedTests;
    TotalTestCount = TotalTests;
    CurrentTestProgress = TotalTests > 0 ? static_cast<float>(CompletedTests) / TotalTests : 0.0f;
}

void UTestRunner::NotifyTestCompleted(const FString& TestName, const FTestResultDetail& Result)
{
    OnTestCompleted.Broadcast(TestName, Result);

    FString StatusText = (Result.Status == ETestResultStatus::Passed) ? TEXT("PASS") : TEXT("FAIL");
    UE_LOG(LogTemp, Log, TEXT("【测试完成】[%s] %s - 执行时间: %.2f ms"),
           *StatusText, *TestName, Result.ExecutionTimeMs);
}

void UTestRunner::NotifyTestSuiteCompleted(const TArray<FTestResultDetail>& Results, const FTestSuiteConfig& Config)
{
    OnTestSuiteCompleted.Broadcast(Results, Config);

    int32 PassedTests = 0;
    for (const FTestResultDetail& Result : Results)
    {
        if (Result.Status == ETestResultStatus::Passed)
        {
            PassedTests++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("【测试套件完成】总计: %d, 通过: %d, 失败: %d"),
           Results.Num(), PassedTests, Results.Num() - PassedTests);
}

bool UTestRunner::InitializeTestEnvironment()
{
    // 获取各个管理器的引用
    if (const UWorld* World = GetWorld())
    {
        // 创建PerformanceOptimizer实例（它是协调器，不是子系统）
        PerformanceOptimizer = NewObject<UPerformanceOptimizer>(this);
        if (IsValid(PerformanceOptimizer))
        {
            // 初始化PerformanceOptimizer，它会自动获取其他子系统的引用
            FPerformanceOptimizationParams DefaultParams;
            DefaultParams.bEnableCaching = true;
            DefaultParams.bEnableObjectPooling = true;
            DefaultParams.bEnableGPUCompute = true;
            PerformanceOptimizer->Initialize(DefaultParams);
        }

        // 获取子系统引用
        CacheManager = UCacheManager::Get(World);
        GPUComputeManager = UGPUComputeManager::Get(World);
        ObjectPoolManager = UObjectPoolManager::Get(World);

        if (!IsValid(PerformanceOptimizer))
        {
            UE_LOG(LogTemp, Warning, TEXT("【测试运行器】PerformanceOptimizer 不可用"));
        }

        if (!IsValid(CacheManager))
        {
            UE_LOG(LogTemp, Warning, TEXT("【测试运行器】CacheManager 不可用"));
        }

        if (!IsValid(GPUComputeManager))
        {
            UE_LOG(LogTemp, Warning, TEXT("【测试运行器】GPUComputeManager 不可用"));
        }

        if (!IsValid(ObjectPoolManager))
        {
            UE_LOG(LogTemp, Warning, TEXT("【测试运行器】ObjectPoolManager 不可用"));
        }

        return true;
    }

    return false;
}

void UTestRunner::CleanupTestEnvironment()
{
    PerformanceOptimizer = nullptr;
    CacheManager = nullptr;
    GPUComputeManager = nullptr;
    ObjectPoolManager = nullptr;
}

bool UTestRunner::ValidateTestPreconditions(const FTestSuiteConfig& Config)
{
    // 基本验证
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("【测试运行器】无效的世界对象"));
        return false;
    }

    // 基本的模块可用性检查已在InitializeTestEnvironment中完成
    // 这里只做基本验证

    return true;
}
