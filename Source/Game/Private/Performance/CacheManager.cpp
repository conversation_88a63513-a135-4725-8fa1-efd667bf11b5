#include "Performance/CacheManager.h"
#include "Engine/World.h"
#include "HAL/PlatformTime.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Compression/OodleDataCompression.h"
#include "Async/AsyncWork.h"

/**
 * 【单一职责原则】缓存管理器实现文件
 * 
 * 实现UCacheManager的所有方法，严格遵循单一职责原则，
 * 只负责缓存系统管理功能。支持多级缓存、异步操作和智能预测。
 */

UCacheManager* UCacheManager::Get(const UWorld* World)
{
    if (LIKELY(World))
    {
        return World->GetSubsystem<UCacheManager>();
    }
    return nullptr;
}

bool UCacheManager::ShouldCreateSubsystem(UObject* Outer) const
{
    // 【条件创建】只在游戏世界和编辑器世界中创建
    if (!Super::ShouldCreateSubsystem(Outer))
    {
        return false;
    }

    if (const UWorld* World = Cast<UWorld>(Outer))
    {
        // 【世界类型检查】支持游戏世界、PIE世界和编辑器世界
        const EWorldType::Type WorldType = World->WorldType;
        return WorldType == EWorldType::Game || 
               WorldType == EWorldType::PIE || 
               WorldType == EWorldType::Editor;
    }

    return false;
}

void UCacheManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // 【初始化】设置初始状态
    CacheExpirationTime = 300.0f; // 5分钟默认过期时间
    MaxL1CacheSize = 100 * 1024 * 1024; // 100MB
    MaxL2CacheSize = 500 * 1024 * 1024; // 500MB

    // 【统计初始化】
    CacheStatistics = FCacheStats();

    // 【磁盘缓存目录】确保磁盘缓存目录存在
    const FString CacheDir = FPaths::ProjectSavedDir() / TEXT("Cache") / TEXT("Performance");
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*CacheDir))
    {
        PlatformFile.CreateDirectoryTree(*CacheDir);
    }

    // 【异步压缩初始化】使用全局线程池进行压缩任务
    // 注意：UE5中FQueuedThreadPool是抽象类，我们使用全局线程池
    CompressionThreadPool = nullptr; // 使用全局线程池，不需要创建专用线程池
    const int32 CompressionThreadCount = FMath::Max(1, FPlatformMisc::NumberOfCoresIncludingHyperthreads() / 4);

    UE_LOG(LogTemp, Log, TEXT("【缓存管理器】初始化完成，缓存目录: %s，压缩线程数: %d"),
           *CacheDir, CompressionThreadCount);
}

void UCacheManager::Deinitialize()
{
    // 【异步任务清理】等待当前清理任务完成
    if (CurrentCleanupTask.IsValid())
    {
        CurrentCleanupTask->EnsureCompletion();
        CurrentCleanupTask.Reset();
    }

    // 【异步压缩清理】由于使用全局线程池，无需特殊清理
    // CompressionThreadPool 已设置为 nullptr，无需清理

    // 【缓存清理】清理所有缓存
    ClearAllCache();

    // 【内存映射清理】清理所有内存映射
    {
        FScopeLock Lock(&MappingMutex);
        for (auto& Pair : MappedRegions)
        {
            if (Pair.Value.MappedMemory)
            {
                FMemory::Free(Pair.Value.MappedMemory);
            }
        }
        MappedRegions.Empty();
        UE_LOG(LogTemp, Log, TEXT("【缓存管理器】清理了所有内存映射"));
    }

    // 【高级预测清理】清理所有访问模式
    {
        FScopeLock Lock(&PredictionMutex);
        AccessPatterns.Empty();
        UE_LOG(LogTemp, Log, TEXT("【缓存管理器】清理了所有访问模式"));
    }

    Super::Deinitialize();

    UE_LOG(LogTemp, Log, TEXT("【缓存管理器】反初始化完成"));
}

void UCacheManager::CacheData(const FString& Key, const TArray<FMapCell>& Data, ECacheLevel Level)
{
    if (Key.IsEmpty() || Data.Num() == 0)
    {
        return;
    }

    FScopeLock Lock(&CacheMutex);

    // 【创建缓存条目】
    FCacheEntry Entry;
    Entry.Key = Key;
    Entry.Level = Level;
    Entry.CreationTime = FPlatformTime::Seconds();
    Entry.LastAccessTime = Entry.CreationTime;
    Entry.AccessCount = 1;

    // 【数据序列化】使用新的序列化方法，包含版本和校验
    if (!SerializeMapCellData(Data, Entry.RawData))
    {
        UE_LOG(LogTemp, Error, TEXT("【缓存管理器】数据序列化失败: %s"), *Key);
        return;
    }
    Entry.RawDataSize = Entry.RawData.Num();

    // 【根据级别处理数据】
    switch (Level)
    {
        case ECacheLevel::L1_Memory:
        {
            // 【L1缓存】直接存储在内存中，不压缩
            Entry.bIsCompressed = false;
            Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
            Entry.CompressedDataSize = Entry.RawDataSize;
            break;
        }
        case ECacheLevel::L2_Compressed:
        {
            // 【L2缓存】使用自适应压缩算法选择
            ECompressionAlgorithm OptimalAlgorithm = SelectOptimalCompressionAlgorithm(Entry.RawData);

            if (OptimalAlgorithm != ECompressionAlgorithm::None &&
                CompressData(Entry.RawData, Entry.CompressedData, OptimalAlgorithm))
            {
                Entry.bIsCompressed = true;
                Entry.CompressionAlgorithm = OptimalAlgorithm;
                Entry.CompressedDataSize = Entry.CompressedData.Num();

                UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】L2缓存压缩成功: %s，算法: %d，压缩率: %.2f%%"),
                       *Key, static_cast<int32>(OptimalAlgorithm),
                       (float)Entry.CompressedDataSize / Entry.RawDataSize * 100.0f);
            }
            else
            {
                // 【压缩失败或不需要压缩】回退到不压缩
                Entry.bIsCompressed = false;
                Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
                Entry.CompressedDataSize = Entry.RawDataSize;

                UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】L2缓存不压缩: %s"), *Key);
            }
            break;
        }
        case ECacheLevel::L3_Disk:
        {
            // 【L3缓存】使用高压缩比算法存储到磁盘
            TArray<uint8> DataToWrite = Entry.RawData;

            // 【优先使用Oodle】L3缓存优先考虑压缩比
            ECompressionAlgorithm DiskAlgorithm = ECompressionAlgorithm::ZSTD; // Oodle

            // 【自适应选择】如果数据特征不适合高压缩比，选择其他算法
            ECompressionAlgorithm OptimalAlgorithm = SelectOptimalCompressionAlgorithm(Entry.RawData);
            if (OptimalAlgorithm == ECompressionAlgorithm::None)
            {
                DiskAlgorithm = ECompressionAlgorithm::LZ4; // 至少使用快速压缩
            }
            else if (OptimalAlgorithm != ECompressionAlgorithm::ZSTD)
            {
                DiskAlgorithm = OptimalAlgorithm;
            }

            if (CompressData(Entry.RawData, Entry.CompressedData, DiskAlgorithm))
            {
                Entry.bIsCompressed = true;
                Entry.CompressionAlgorithm = DiskAlgorithm;
                Entry.CompressedDataSize = Entry.CompressedData.Num();
                DataToWrite = Entry.CompressedData;

                UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】L3缓存压缩成功: %s，算法: %d，压缩率: %.2f%%"),
                       *Key, static_cast<int32>(DiskAlgorithm),
                       (float)Entry.CompressedDataSize / Entry.RawDataSize * 100.0f);
            }
            else
            {
                Entry.bIsCompressed = false;
                Entry.CompressionAlgorithm = ECompressionAlgorithm::None;
                Entry.CompressedDataSize = Entry.RawDataSize;

                UE_LOG(LogTemp, Warning, TEXT("【缓存管理器】L3缓存压缩失败，使用原始数据: %s"), *Key);
            }

            // 【写入磁盘】
            if (WriteToDisk(Key, DataToWrite))
            {
                Entry.DiskFilePath = GetDiskCacheFilePath(Key);
                // 【内存优化】L3缓存不在内存中保留数据
                Entry.RawData.Empty();
                Entry.CompressedData.Empty();
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("【缓存管理器】写入磁盘失败: %s"), *Key);
                return;
            }
            break;
        }
    }

    // 【存储缓存条目】
    CacheEntries.Add(Key, Entry);

    // 【更新统计】
    UpdateCacheStatistics(Level, Entry.RawDataSize);

    // 【容量检查】检查并清理超出容量的缓存
    CheckAndCleanupCapacity();

    UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】缓存数据: %s (级别: %d, 大小: %d 字节)"),
           *Key, static_cast<int32>(Level), Entry.RawDataSize);
}

bool UCacheManager::GetCachedData(const FString& Key, TArray<FMapCell>& OutData)
{
    if (Key.IsEmpty())
    {
        return false;
    }

    const double StartTime = FPlatformTime::Seconds();
    FScopeLock Lock(&CacheMutex);

    // 【查找缓存条目】
    FCacheEntry* Entry = CacheEntries.Find(Key);
    if (!Entry)
    {
        // 【缓存未命中】
        CacheStatistics.Misses++;
        return false;
    }

    // 【更新访问信息】
    const double CurrentTime = FPlatformTime::Seconds();
    Entry->LastAccessTime = CurrentTime;
    Entry->AccessCount++;

    // 【更新预测概率】
    UpdatePredictionProbability(*Entry);

    // 【高级预测学习】学习访问模式
    LearnAccessPattern(Key, CurrentTime);

    TArray<uint8> DataToDeserialize;
    bool bDataRetrieved = false;

    // 【根据级别获取数据】
    switch (Entry->Level)
    {
        case ECacheLevel::L1_Memory:
        {
            // 【L1缓存命中】直接从内存获取
            DataToDeserialize = Entry->RawData;
            bDataRetrieved = true;
            CacheStatistics.L1Hits++;
            break;
        }
        case ECacheLevel::L2_Compressed:
        {
            // 【L2缓存命中】从内存获取并解压
            if (Entry->bIsCompressed)
            {
                bDataRetrieved = DecompressData(Entry->CompressedData, DataToDeserialize, Entry->CompressionAlgorithm);
            }
            else
            {
                DataToDeserialize = Entry->RawData;
                bDataRetrieved = true;
            }
            CacheStatistics.L2Hits++;
            break;
        }
        case ECacheLevel::L3_Disk:
        {
            // 【L3缓存命中】从磁盘读取
            TArray<uint8> DiskData;
            if (ReadFromDisk(Key, DiskData))
            {
                if (Entry->bIsCompressed)
                {
                    bDataRetrieved = DecompressData(DiskData, DataToDeserialize, Entry->CompressionAlgorithm);
                }
                else
                {
                    DataToDeserialize = DiskData;
                    bDataRetrieved = true;
                }
            }
            CacheStatistics.L3Hits++;
            break;
        }
    }

    if (!bDataRetrieved)
    {
        UE_LOG(LogTemp, Warning, TEXT("【缓存管理器】数据获取失败: %s"), *Key);
        CacheStatistics.Misses++;
        return false;
    }

    // 【反序列化数据】使用新的反序列化方法，包含版本和校验
    if (DeserializeMapCellData(DataToDeserialize, OutData))
    {
        // 【更新访问时间统计】
        const double AccessTime = (FPlatformTime::Seconds() - StartTime) * 1000.0; // 转换为毫秒
        UpdateAccessTimeStatistics(AccessTime);

        UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】缓存命中: %s (级别: %d, %d个单元格, 耗时: %.2f ms)"),
               *Key, static_cast<int32>(Entry->Level), OutData.Num(), AccessTime);

        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("【缓存管理器】数据反序列化失败: %s"), *Key);
    CacheStatistics.Misses++;
    return false;
}

void UCacheManager::CleanupExpiredCache()
{
    // 【异步清理】如果当前没有清理任务在运行，启动新的清理任务
    if (!CurrentCleanupTask.IsValid() || CurrentCleanupTask->IsDone())
    {
        CurrentCleanupTask = MakeUnique<FAsyncTask<FAsyncCacheCleanupTask>>(&CacheEntries, &CacheMutex);
        CurrentCleanupTask->StartBackgroundTask();
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("【缓存管理器】启动异步缓存清理任务"));
    }
}

void UCacheManager::ClearAllCache()
{
    FScopeLock Lock(&CacheMutex);

    // 【清理磁盘文件】
    for (const auto& CachePair : CacheEntries)
    {
        const FCacheEntry& Entry = CachePair.Value;
        if (Entry.Level == ECacheLevel::L3_Disk && !Entry.DiskFilePath.IsEmpty())
        {
            IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
            if (PlatformFile.FileExists(*Entry.DiskFilePath))
            {
                PlatformFile.DeleteFile(*Entry.DiskFilePath);
            }
        }
    }

    // 【清理内存缓存】
    CacheEntries.Empty();

    // 【重置统计】
    CacheStatistics = FCacheStats();

    UE_LOG(LogTemp, Log, TEXT("【缓存管理器】清理所有缓存"));
}

FCacheStats UCacheManager::GetCacheStats() const
{
    FScopeLock Lock(&CacheMutex);
    return CacheStatistics;
}

void UCacheManager::PreloadPredictedCache(const TArray<FString>& Keys)
{
    // 【预测缓存】异步预加载预测的缓存数据
    AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [this, Keys]()
    {
        for (const FString& Key : Keys)
        {
            FScopeLock Lock(&CacheMutex);
            
            FCacheEntry* Entry = CacheEntries.Find(Key);
            if (Entry && Entry->PredictedAccessProbability > 0.7f) // 只预加载高概率的缓存
            {
                // 【预测命中】如果预测正确，更新统计
                if (Entry->NextPredictedAccessTime > 0.0 && 
                    FMath::Abs(FPlatformTime::Seconds() - Entry->NextPredictedAccessTime) < 60.0) // 1分钟内
                {
                    CacheStatistics.PredictionHits++;
                }

                // 【预加载到L1】将L2/L3缓存提升到L1
                if (Entry->Level != ECacheLevel::L1_Memory)
                {
                    TArray<FMapCell> TempData;
                    if (GetCachedData(Key, TempData))
                    {
                        CacheData(Key, TempData, ECacheLevel::L1_Memory);
                        UE_LOG(LogTemp, VeryVerbose, TEXT("【预测缓存】预加载到L1: %s"), *Key);
                    }
                }
            }
            else if (Entry)
            {
                CacheStatistics.PredictionMisses++;
            }
        }
    });
}

void UCacheManager::SetCacheExpirationTime(float ExpirationTime)
{
    CacheExpirationTime = FMath::Max(ExpirationTime, 10.0f); // 最少10秒
    UE_LOG(LogTemp, Log, TEXT("【缓存管理器】设置缓存过期时间: %.1f 秒"), CacheExpirationTime);
}

// 【异步清理任务实现】
void FAsyncCacheCleanupTask::DoWork()
{
    if (!CacheEntries || !CacheMutex)
    {
        return;
    }

    FScopeLock Lock(CacheMutex);
    
    const double CurrentTime = FPlatformTime::Seconds();
    TArray<FString> KeysToRemove;

    // 【查找过期条目】
    for (const auto& CachePair : *CacheEntries)
    {
        const FString& Key = CachePair.Key;
        const FCacheEntry& Entry = CachePair.Value;

        // 【过期检查】检查是否过期（默认5分钟）
        if ((CurrentTime - Entry.LastAccessTime) > 300.0)
        {
            KeysToRemove.Add(Key);
        }
    }

    // 【清理过期条目】
    for (const FString& Key : KeysToRemove)
    {
        FCacheEntry* Entry = CacheEntries->Find(Key);
        if (Entry)
        {
            // 【清理磁盘文件】
            if (Entry->Level == ECacheLevel::L3_Disk && !Entry->DiskFilePath.IsEmpty())
            {
                IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
                if (PlatformFile.FileExists(*Entry->DiskFilePath))
                {
                    PlatformFile.DeleteFile(*Entry->DiskFilePath);
                }
            }
            
            CacheEntries->Remove(Key);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("【异步缓存清理】清理了 %d 个过期条目"), KeysToRemove.Num());
}

// 【辅助方法实现】

bool UCacheManager::CompressData(const TArray<uint8>& RawData, TArray<uint8>& CompressedData, ECompressionAlgorithm Algorithm)
{
    if (RawData.Num() == 0)
    {
        return false;
    }

    // 【性能统计】记录压缩开始时间
    const double StartTime = FPlatformTime::Seconds();

    switch (Algorithm)
    {
        case ECompressionAlgorithm::LZ4:
        {
            // 【LZ4压缩】使用UE5内置的LZ4压缩算法
            const int64 UncompressedSize = RawData.Num();
            int64 MaxCompressedSize = 0;

            if (!FCompression::GetMaximumCompressedSize(NAME_LZ4, MaxCompressedSize, UncompressedSize))
            {
                UE_LOG(LogTemp, Warning, TEXT("【压缩算法】无法获取LZ4最大压缩大小"));
                return false;
            }

            CompressedData.SetNumUninitialized(MaxCompressedSize);
            int64 CompressedSize = MaxCompressedSize;

            const bool bSuccess = FCompression::CompressMemory(
                NAME_LZ4,
                CompressedData.GetData(),
                CompressedSize,
                RawData.GetData(),
                UncompressedSize,
                COMPRESS_BiasSpeed  // 【优化】LZ4优先速度
            );

            if (bSuccess && CompressedSize < UncompressedSize)
            {
                CompressedData.SetNum(CompressedSize);
                UE_LOG(LogTemp, VeryVerbose, TEXT("【压缩算法】LZ4压缩成功: %d -> %lld 字节 (%.2f%%)"),
                       RawData.Num(), CompressedSize, (float)CompressedSize / RawData.Num() * 100.0f);
                return true;
            }
            break;
        }
        case ECompressionAlgorithm::Zlib:
        {
            // 【Zlib压缩】使用UE5内置的Zlib压缩算法
            const int64 UncompressedSize = RawData.Num();
            int64 MaxCompressedSize = 0;

            if (!FCompression::GetMaximumCompressedSize(NAME_Zlib, MaxCompressedSize, UncompressedSize))
            {
                UE_LOG(LogTemp, Warning, TEXT("【压缩算法】无法获取Zlib最大压缩大小"));
                return false;
            }

            CompressedData.SetNumUninitialized(MaxCompressedSize);
            int64 CompressedSize = MaxCompressedSize;

            const bool bSuccess = FCompression::CompressMemory(
                NAME_Zlib,
                CompressedData.GetData(),
                CompressedSize,
                RawData.GetData(),
                UncompressedSize,
                COMPRESS_NoFlags  // 【平衡】Zlib使用默认设置
            );

            if (bSuccess && CompressedSize < UncompressedSize)
            {
                CompressedData.SetNum(CompressedSize);
                UE_LOG(LogTemp, VeryVerbose, TEXT("【压缩算法】Zlib压缩成功: %d -> %lld 字节 (%.2f%%)"),
                       RawData.Num(), CompressedSize, (float)CompressedSize / RawData.Num() * 100.0f);
                return true;
            }
            break;
        }
        case ECompressionAlgorithm::ZSTD:
        {
            // 【Oodle压缩】使用UE5内置的Oodle压缩算法（高压缩比）
            const int64 UncompressedSize = RawData.Num();
            int64 MaxCompressedSize = 0;

            if (!FCompression::GetMaximumCompressedSize(NAME_Oodle, MaxCompressedSize, UncompressedSize))
            {
                UE_LOG(LogTemp, Warning, TEXT("【压缩算法】无法获取Oodle最大压缩大小，回退到Zlib"));
                // 【回退机制】如果Oodle不可用，回退到Zlib
                return CompressData(RawData, CompressedData, ECompressionAlgorithm::Zlib);
            }

            CompressedData.SetNumUninitialized(MaxCompressedSize);
            int64 CompressedSize = MaxCompressedSize;

            const bool bSuccess = FCompression::CompressMemory(
                NAME_Oodle,
                CompressedData.GetData(),
                CompressedSize,
                RawData.GetData(),
                UncompressedSize,
                COMPRESS_BiasSize  // 【优化】Oodle优先压缩比
            );

            if (bSuccess && CompressedSize < UncompressedSize)
            {
                CompressedData.SetNum(CompressedSize);
                UE_LOG(LogTemp, VeryVerbose, TEXT("【压缩算法】Oodle压缩成功: %d -> %lld 字节 (%.2f%%)"),
                       RawData.Num(), CompressedSize, (float)CompressedSize / RawData.Num() * 100.0f);
                return true;
            }
            break;
        }
        case ECompressionAlgorithm::Adaptive:
        {
            // 【自适应压缩】根据数据特征选择最优算法
            ECompressionAlgorithm OptimalAlgorithm = SelectOptimalCompressionAlgorithm(RawData);
            if (OptimalAlgorithm != ECompressionAlgorithm::Adaptive)
            {
                return CompressData(RawData, CompressedData, OptimalAlgorithm);
            }
            // 【回退】如果自适应选择失败，使用Zlib
            return CompressData(RawData, CompressedData, ECompressionAlgorithm::Zlib);
        }
        case ECompressionAlgorithm::None:
        default:
        {
            CompressedData = RawData;
            return true;
        }
    }

    // 【压缩失败处理】记录失败信息并返回原始数据
    UE_LOG(LogTemp, Warning, TEXT("【压缩算法】压缩失败，算法: %d，数据大小: %d"),
           static_cast<int32>(Algorithm), RawData.Num());

    // 【性能统计】记录压缩时间
    const double CompressionTime = (FPlatformTime::Seconds() - StartTime) * 1000.0;
    UE_LOG(LogTemp, VeryVerbose, TEXT("【压缩算法】压缩耗时: %.2f ms"), CompressionTime);

    return false;
}

bool UCacheManager::DecompressData(const TArray<uint8>& CompressedData, TArray<uint8>& RawData, ECompressionAlgorithm Algorithm)
{
    if (CompressedData.Num() == 0)
    {
        return false;
    }

    // 【性能统计】记录解压开始时间
    const double StartTime = FPlatformTime::Seconds();

    switch (Algorithm)
    {
        case ECompressionAlgorithm::LZ4:
        {
            // 【LZ4解压】使用UE5内置的LZ4解压算法
            int64 UncompressedSize = 0;

            // 【获取解压大小】
            if (!FCompression::UncompressMemory(
                NAME_LZ4,
                nullptr,
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num(),
                COMPRESS_NoFlags,
                0))
            {
                UE_LOG(LogTemp, Warning, TEXT("【解压算法】无法获取LZ4解压大小"));
                return false;
            }

            RawData.SetNumUninitialized(UncompressedSize);

            const bool bSuccess = FCompression::UncompressMemory(
                NAME_LZ4,
                RawData.GetData(),
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num()
            );

            if (bSuccess)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("【解压算法】LZ4解压成功: %d -> %lld 字节"),
                       CompressedData.Num(), UncompressedSize);
            }
            return bSuccess;
        }
        case ECompressionAlgorithm::Zlib:
        {
            // 【Zlib解压】使用UE5内置的Zlib解压算法
            int64 UncompressedSize = 0;

            // 【获取解压大小】
            if (!FCompression::UncompressMemory(
                NAME_Zlib,
                nullptr,
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num(),
                COMPRESS_NoFlags,
                0))
            {
                UE_LOG(LogTemp, Warning, TEXT("【解压算法】无法获取Zlib解压大小"));
                return false;
            }

            RawData.SetNumUninitialized(UncompressedSize);

            const bool bSuccess = FCompression::UncompressMemory(
                NAME_Zlib,
                RawData.GetData(),
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num()
            );

            if (bSuccess)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("【解压算法】Zlib解压成功: %d -> %lld 字节"),
                       CompressedData.Num(), UncompressedSize);
            }
            return bSuccess;
        }
        case ECompressionAlgorithm::ZSTD:
        {
            // 【Oodle解压】使用UE5内置的Oodle解压算法
            int64 UncompressedSize = 0;

            // 【获取解压大小】
            if (!FCompression::UncompressMemory(
                NAME_Oodle,
                nullptr,
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num(),
                COMPRESS_NoFlags,
                0))
            {
                UE_LOG(LogTemp, Warning, TEXT("【解压算法】无法获取Oodle解压大小，尝试Zlib回退"));
                // 【回退机制】如果Oodle解压失败，尝试Zlib
                return DecompressData(CompressedData, RawData, ECompressionAlgorithm::Zlib);
            }

            RawData.SetNumUninitialized(UncompressedSize);

            const bool bSuccess = FCompression::UncompressMemory(
                NAME_Oodle,
                RawData.GetData(),
                UncompressedSize,
                CompressedData.GetData(),
                CompressedData.Num()
            );

            if (bSuccess)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("【解压算法】Oodle解压成功: %d -> %lld 字节"),
                       CompressedData.Num(), UncompressedSize);
            }
            return bSuccess;
        }
        case ECompressionAlgorithm::None:
        default:
        {
            RawData = CompressedData;
            return true;
        }
    }
}

bool UCacheManager::WriteToDisk(const FString& Key, const TArray<uint8>& Data)
{
    const FString FilePath = GetDiskCacheFilePath(Key);
    return FFileHelper::SaveArrayToFile(Data, *FilePath);
}

bool UCacheManager::ReadFromDisk(const FString& Key, TArray<uint8>& Data)
{
    const FString FilePath = GetDiskCacheFilePath(Key);
    return FFileHelper::LoadFileToArray(Data, *FilePath);
}

void UCacheManager::UpdatePredictionProbability(FCacheEntry& Entry)
{
    // 【智能预测算法】基于访问模式更新预测概率
    const double CurrentTime = FPlatformTime::Seconds();
    const double TimeSinceLastAccess = CurrentTime - Entry.LastAccessTime;

    // 【访问频率因子】访问越频繁，预测概率越高
    const float FrequencyFactor = FMath::Clamp(Entry.AccessCount / 10.0f, 0.1f, 1.0f);

    // 【时间衰减因子】时间越久，预测概率越低
    const float TimeDecayFactor = FMath::Exp(-TimeSinceLastAccess / 3600.0); // 1小时衰减

    // 【计算新的预测概率】
    Entry.PredictedAccessProbability = FrequencyFactor * TimeDecayFactor;

    // 【预测下次访问时间】基于历史访问模式
    if (Entry.AccessCount > 1)
    {
        const double AverageAccessInterval = (CurrentTime - Entry.CreationTime) / Entry.AccessCount;
        Entry.NextPredictedAccessTime = CurrentTime + AverageAccessInterval;
    }
}

void UCacheManager::CheckAndCleanupCapacity()
{
    // 【容量检查】检查L1和L2缓存是否超出限制
    int64 CurrentL1Size = 0;
    int64 CurrentL2Size = 0;

    for (const auto& CachePair : CacheEntries)
    {
        const FCacheEntry& Entry = CachePair.Value;

        switch (Entry.Level)
        {
            case ECacheLevel::L1_Memory:
                CurrentL1Size += Entry.RawDataSize;
                break;
            case ECacheLevel::L2_Compressed:
                CurrentL2Size += Entry.CompressedDataSize;
                break;
            default:
                break;
        }
    }

    // 【L1容量清理】如果L1缓存超出限制，移除最少使用的条目
    if (CurrentL1Size > MaxL1CacheSize)
    {
        TArray<TPair<FString, double>> L1Entries;

        for (const auto& CachePair : CacheEntries)
        {
            if (CachePair.Value.Level == ECacheLevel::L1_Memory)
            {
                L1Entries.Add(TPair<FString, double>(CachePair.Key, CachePair.Value.LastAccessTime));
            }
        }

        // 【按访问时间排序】最久未访问的排在前面
        L1Entries.Sort([](const TPair<FString, double>& A, const TPair<FString, double>& B)
        {
            return A.Value < B.Value;
        });

        // 【移除最久未使用的条目】
        const int32 EntriesToRemove = FMath::Max(1, L1Entries.Num() / 4); // 移除25%
        for (int32 i = 0; i < EntriesToRemove && i < L1Entries.Num(); ++i)
        {
            CacheEntries.Remove(L1Entries[i].Key);
        }

        UE_LOG(LogTemp, VeryVerbose, TEXT("【容量清理】L1缓存清理了 %d 个条目"), EntriesToRemove);
    }

    // 【L2容量清理】类似的逻辑处理L2缓存
    if (CurrentL2Size > MaxL2CacheSize)
    {
        TArray<TPair<FString, double>> L2Entries;

        for (const auto& CachePair : CacheEntries)
        {
            if (CachePair.Value.Level == ECacheLevel::L2_Compressed)
            {
                L2Entries.Add(TPair<FString, double>(CachePair.Key, CachePair.Value.LastAccessTime));
            }
        }

        L2Entries.Sort([](const TPair<FString, double>& A, const TPair<FString, double>& B)
        {
            return A.Value < B.Value;
        });

        const int32 EntriesToRemove = FMath::Max(1, L2Entries.Num() / 4);
        for (int32 i = 0; i < EntriesToRemove && i < L2Entries.Num(); ++i)
        {
            CacheEntries.Remove(L2Entries[i].Key);
        }

        UE_LOG(LogTemp, VeryVerbose, TEXT("【容量清理】L2缓存清理了 %d 个条目"), EntriesToRemove);
    }
}

FString UCacheManager::GetDiskCacheFilePath(const FString& Key) const
{
    // 【文件路径生成】生成安全的文件名
    FString SafeKey = Key;
    SafeKey = SafeKey.Replace(TEXT("/"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("\\"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT(":"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("*"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("?"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("\""), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("<"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT(">"), TEXT("_"));
    SafeKey = SafeKey.Replace(TEXT("|"), TEXT("_"));

    const FString CacheDir = FPaths::ProjectSavedDir() / TEXT("Cache") / TEXT("Performance");
    return CacheDir / (SafeKey + TEXT(".cache"));
}

void UCacheManager::UpdateCacheStatistics(ECacheLevel Level, int32 DataSize)
{
    // 【统计更新】更新相应级别的内存使用统计
    switch (Level)
    {
        case ECacheLevel::L1_Memory:
            CacheStatistics.L1MemoryUsage += DataSize;
            break;
        case ECacheLevel::L2_Compressed:
            CacheStatistics.L2MemoryUsage += DataSize;
            break;
        case ECacheLevel::L3_Disk:
            CacheStatistics.L3DiskUsage += DataSize;
            break;
    }
}

void UCacheManager::UpdateAccessTimeStatistics(double AccessTimeMs)
{
    // 【访问时间统计】使用指数移动平均更新平均访问时间
    if (CacheStatistics.AverageAccessTime == 0.0f)
    {
        CacheStatistics.AverageAccessTime = AccessTimeMs;
    }
    else
    {
        const float Alpha = 0.1f; // 平滑系数
        CacheStatistics.AverageAccessTime = CacheStatistics.AverageAccessTime * (1.0f - Alpha) + AccessTimeMs * Alpha;
    }
}

// ========== 内存映射缓存实现 ==========

bool UCacheManager::MapCacheFile(const FString& CacheKey, const FString& FilePath, bool bReadOnly)
{
    FScopeLock Lock(&MappingMutex);

    // 【重复检查】检查是否已经映射
    if (MappedRegions.Contains(CacheKey))
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存映射】文件已映射: %s"), *CacheKey);
        return true;
    }

    // 【映射数量限制】检查映射数量限制
    if (MappedRegions.Num() >= MAX_MAPPED_FILES)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存映射】达到最大映射文件数限制: %d"), MAX_MAPPED_FILES);
        CleanupExpiredMappings();  // 尝试清理过期映射

        if (MappedRegions.Num() >= MAX_MAPPED_FILES)
        {
            UE_LOG(LogTemp, Error, TEXT("【内存映射】无法清理足够的映射空间"));
            return false;
        }
    }

    // 【文件检查】检查文件是否存在
    if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】文件不存在: %s"), *FilePath);
        return false;
    }

    // 【文件大小】获取文件大小
    const int64 FileSize = FPlatformFileManager::Get().GetPlatformFile().FileSize(*FilePath);
    if (FileSize <= 0)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】文件大小无效: %s, 大小: %lld"), *FilePath, FileSize);
        return false;
    }

    // 【大小限制】检查文件大小限制
    if (static_cast<size_t>(FileSize) > MAX_MAPPED_SIZE)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】文件过大: %s, 大小: %lld, 限制: %llu"),
               *FilePath, FileSize, MAX_MAPPED_SIZE);
        return false;
    }

    // 【内存分配】分配映射内存
    void* MappedMemory = FMemory::Malloc(FileSize);
    if (!MappedMemory)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】内存分配失败: %lld 字节"), FileSize);
        return false;
    }

    // 【文件读取】读取文件内容到内存
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *FilePath))
    {
        FMemory::Free(MappedMemory);
        UE_LOG(LogTemp, Error, TEXT("【内存映射】文件读取失败: %s"), *FilePath);
        return false;
    }

    // 【内存拷贝】将文件数据拷贝到映射内存
    FMemory::Memcpy(MappedMemory, FileData.GetData(), FileSize);

    // 【创建映射区域】
    FMappedRegion NewRegion;
    NewRegion.FilePath = FilePath;
    NewRegion.MappedMemory = MappedMemory;
    NewRegion.Size = FileSize;
    NewRegion.bIsReadOnly = bReadOnly;
    NewRegion.LastAccessTime = FPlatformTime::Seconds();

    // 【存储映射】
    MappedRegions.Add(CacheKey, NewRegion);

    UE_LOG(LogTemp, Log, TEXT("【内存映射】成功映射文件: %s -> %s, 大小: %lld"),
           *CacheKey, *FilePath, FileSize);

    return true;
}

bool UCacheManager::ReadMappedData(const FString& CacheKey, TArray<FMapCell>& OutData)
{
    FScopeLock Lock(&MappingMutex);

    // 【查找映射】查找指定的映射区域
    FMappedRegion* Region = MappedRegions.Find(CacheKey);
    if (!Region)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存映射】未找到映射: %s"), *CacheKey);
        return false;
    }

    // 【有效性检查】检查映射内存是否有效
    if (!Region->MappedMemory || Region->Size <= 0)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】映射内存无效: %s"), *CacheKey);
        return false;
    }

    // 【数据大小检查】检查数据大小是否为FMapCell的整数倍
    const int32 CellSize = sizeof(FMapCell);
    if (Region->Size % CellSize != 0)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】数据大小不匹配: %s, 大小: %lld, 单元大小: %d"),
               *CacheKey, Region->Size, CellSize);
        return false;
    }

    // 【数据读取】从映射内存读取数据
    const int32 CellCount = Region->Size / CellSize;
    OutData.SetNumUninitialized(CellCount);
    FMemory::Memcpy(OutData.GetData(), Region->MappedMemory, Region->Size);

    // 【更新访问时间】
    Region->LastAccessTime = FPlatformTime::Seconds();

    UE_LOG(LogTemp, VeryVerbose, TEXT("【内存映射】成功读取数据: %s, %d个单元"),
           *CacheKey, CellCount);

    return true;
}

bool UCacheManager::WriteMappedData(const FString& CacheKey, const TArray<FMapCell>& Data)
{
    FScopeLock Lock(&MappingMutex);

    // 【查找映射】查找指定的映射区域
    FMappedRegion* Region = MappedRegions.Find(CacheKey);
    if (!Region)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存映射】未找到映射: %s"), *CacheKey);
        return false;
    }

    // 【只读检查】检查是否为只读映射
    if (Region->bIsReadOnly)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】尝试写入只读映射: %s"), *CacheKey);
        return false;
    }

    // 【有效性检查】检查映射内存是否有效
    if (!Region->MappedMemory || Region->Size <= 0)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】映射内存无效: %s"), *CacheKey);
        return false;
    }

    // 【数据大小检查】检查数据大小是否匹配
    const int64 DataSize = Data.Num() * sizeof(FMapCell);
    if (DataSize > Region->Size)
    {
        UE_LOG(LogTemp, Error, TEXT("【内存映射】数据过大: %s, 数据大小: %lld, 映射大小: %lld"),
               *CacheKey, DataSize, Region->Size);
        return false;
    }

    // 【数据写入】将数据写入映射内存
    FMemory::Memcpy(Region->MappedMemory, Data.GetData(), DataSize);

    // 【更新访问时间】
    Region->LastAccessTime = FPlatformTime::Seconds();

    UE_LOG(LogTemp, VeryVerbose, TEXT("【内存映射】成功写入数据: %s, %d个单元"),
           *CacheKey, Data.Num());

    return true;
}

bool UCacheManager::UnmapCacheFile(const FString& CacheKey)
{
    FScopeLock Lock(&MappingMutex);

    // 【查找映射】查找指定的映射区域
    FMappedRegion* Region = MappedRegions.Find(CacheKey);
    if (!Region)
    {
        UE_LOG(LogTemp, Warning, TEXT("【内存映射】未找到映射: %s"), *CacheKey);
        return false;
    }

    // 【清理内存】释放映射内存
    if (Region->MappedMemory)
    {
        FMemory::Free(Region->MappedMemory);
        Region->MappedMemory = nullptr;
    }

    // 【移除映射】从映射表中移除
    MappedRegions.Remove(CacheKey);

    UE_LOG(LogTemp, Log, TEXT("【内存映射】成功取消映射: %s"), *CacheKey);

    return true;
}

void UCacheManager::CleanupExpiredMappings()
{
    // 【注意】此方法假设已经持有MappingMutex锁

    const double CurrentTime = FPlatformTime::Seconds();
    const double ExpirationThreshold = 300.0; // 5分钟过期时间

    TArray<FString> ExpiredKeys;

    // 【查找过期映射】
    for (auto& Pair : MappedRegions)
    {
        if (IsMappingExpired(Pair.Value))
        {
            ExpiredKeys.Add(Pair.Key);
        }
    }

    // 【清理过期映射】
    for (const FString& Key : ExpiredKeys)
    {
        FMappedRegion* Region = MappedRegions.Find(Key);
        if (Region && Region->MappedMemory)
        {
            FMemory::Free(Region->MappedMemory);
        }
        MappedRegions.Remove(Key);

        UE_LOG(LogTemp, Log, TEXT("【内存映射】清理过期映射: %s"), *Key);
    }

    UE_LOG(LogTemp, Log, TEXT("【内存映射】清理完成，清理了%d个过期映射"), ExpiredKeys.Num());
}

bool UCacheManager::IsMappingExpired(const FMappedRegion& Region) const
{
    const double CurrentTime = FPlatformTime::Seconds();
    const double ExpirationThreshold = 300.0; // 5分钟过期时间

    return (CurrentTime - Region.LastAccessTime) > ExpirationThreshold;
}

// ========== 高级预测缓存实现 ==========

TArray<FString> UCacheManager::PredictNextAccess(const FString& CurrentKey)
{
    FScopeLock Lock(&PredictionMutex);

    TArray<FString> PredictedKeys;

    // 【模式查找】查找当前键的访问模式
    FAccessPattern* Pattern = AccessPatterns.Find(CurrentKey);
    if (!Pattern || Pattern->RecentKeys.Num() < 2)
    {
        // 【无模式】没有足够的历史数据进行预测
        UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】无足够历史数据预测: %s"), *CurrentKey);
        return PredictedKeys;
    }

    // 【序列模式匹配】基于最近的访问序列预测下一个键
    TMap<FString, int32> NextKeyFrequency;  // 下一个键的出现频率
    TMap<FString, float> NextKeyWeights;    // 下一个键的权重

    const int32 HistorySize = Pattern->RecentKeys.Num();

    // 【模式分析】分析不同长度的序列模式
    for (int32 PatternLength = 1; PatternLength <= FMath::Min(3, HistorySize - 1); ++PatternLength)
    {
        // 【当前序列】获取当前序列的最后PatternLength个键
        TArray<FString> CurrentSequence;
        for (int32 i = HistorySize - PatternLength; i < HistorySize; ++i)
        {
            CurrentSequence.Add(Pattern->RecentKeys[i]);
        }

        // 【历史匹配】在历史中查找相同的序列
        for (int32 i = 0; i <= HistorySize - PatternLength - 1; ++i)
        {
            bool bSequenceMatch = true;

            // 【序列比较】检查序列是否匹配
            for (int32 j = 0; j < PatternLength; ++j)
            {
                if (Pattern->RecentKeys[i + j] != CurrentSequence[j])
                {
                    bSequenceMatch = false;
                    break;
                }
            }

            if (bSequenceMatch && i + PatternLength < HistorySize)
            {
                // 【找到匹配】记录下一个键
                const FString& NextKey = Pattern->RecentKeys[i + PatternLength];
                NextKeyFrequency.FindOrAdd(NextKey, 0)++;

                // 【权重计算】较长的序列匹配权重更高
                float Weight = CalculateSequenceWeight(CurrentSequence, PatternLength);
                NextKeyWeights.FindOrAdd(NextKey, 0.0f) += Weight;
            }
        }
    }

    // 【时间衰减】考虑时间因素，最近的访问权重更高
    for (int32 i = 0; i < Pattern->AccessTimes.Num(); ++i)
    {
        const float TimeWeight = CalculateTimeDecayWeight(Pattern->AccessTimes[i]);

        if (i + 1 < Pattern->RecentKeys.Num())
        {
            const FString& NextKey = Pattern->RecentKeys[i + 1];
            NextKeyWeights.FindOrAdd(NextKey, 0.0f) += TimeWeight;
        }
    }

    // 【结果排序】按权重排序预测结果
    NextKeyWeights.ValueSort([](const float& A, const float& B) {
        return A > B;  // 降序排列
    });

    // 【置信度计算】计算预测置信度
    float TotalWeight = 0.0f;
    for (const auto& WeightPair : NextKeyWeights)
    {
        TotalWeight += WeightPair.Value;
    }

    // 【结果生成】生成预测结果
    for (const auto& WeightPair : NextKeyWeights)
    {
        const FString& PredictedKey = WeightPair.Key;
        const float Weight = WeightPair.Value;
        const float Confidence = TotalWeight > 0.0f ? Weight / TotalWeight : 0.0f;

        if (Confidence >= MIN_CONFIDENCE)
        {
            PredictedKeys.Add(PredictedKey);

            UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】预测键: %s (置信度: %.2f)"),
                   *PredictedKey, Confidence);
        }

        // 【限制数量】最多预测5个键
        if (PredictedKeys.Num() >= 5)
        {
            break;
        }
    }

    // 【更新统计】更新预测置信度
    if (PredictedKeys.Num() > 0)
    {
        Pattern->PredictionConfidence = NextKeyWeights.begin()->Value / TotalWeight;
        Pattern->TotalPredictions++;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】为键 %s 预测了 %d 个后续键"),
           *CurrentKey, PredictedKeys.Num());

    return PredictedKeys;
}

void UCacheManager::LearnAccessPattern(const FString& Key, double AccessTime)
{
    FScopeLock Lock(&PredictionMutex);

    // 【获取或创建模式】
    FAccessPattern& Pattern = AccessPatterns.FindOrAdd(Key);

    // 【记录访问】添加到访问历史
    Pattern.RecentKeys.Add(Key);
    Pattern.AccessTimes.Add(AccessTime);

    // 【历史限制】保持历史记录在合理范围内
    if (Pattern.RecentKeys.Num() > MAX_HISTORY_SIZE)
    {
        Pattern.RecentKeys.RemoveAt(0);
        Pattern.AccessTimes.RemoveAt(0);
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】学习访问模式: %s (历史数量: %d)"),
           *Key, Pattern.RecentKeys.Num());
}

void UCacheManager::UpdatePredictionAccuracy(const FString& PredictedKey, const FString& ActualKey)
{
    FScopeLock Lock(&PredictionMutex);

    // 【查找预测模式】
    for (auto& PatternPair : AccessPatterns)
    {
        FAccessPattern& Pattern = PatternPair.Value;

        if (Pattern.TotalPredictions > 0)
        {
            if (PredictedKey == ActualKey)
            {
                Pattern.SuccessfulPredictions++;
                UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】预测成功: %s"), *PredictedKey);
            }

            // 【更新置信度】基于成功率更新置信度
            Pattern.PredictionConfidence = static_cast<float>(Pattern.SuccessfulPredictions) /
                                         static_cast<float>(Pattern.TotalPredictions);
        }
    }
}

float UCacheManager::GetAdvancedPredictionAccuracy() const
{
    FScopeLock Lock(&PredictionMutex);

    int32 TotalSuccessful = 0;
    int32 TotalPredictions = 0;

    // 【统计所有模式】
    for (const auto& PatternPair : AccessPatterns)
    {
        const FAccessPattern& Pattern = PatternPair.Value;
        TotalSuccessful += Pattern.SuccessfulPredictions;
        TotalPredictions += Pattern.TotalPredictions;
    }

    return TotalPredictions > 0 ? static_cast<float>(TotalSuccessful) / TotalPredictions : 0.0f;
}

void UCacheManager::CleanupExpiredAccessPatterns()
{
    // 【注意】此方法假设已经持有PredictionMutex锁

    const double CurrentTime = FPlatformTime::Seconds();
    TArray<FString> KeysToRemove;

    // 【查找过期模式】
    for (const auto& PatternPair : AccessPatterns)
    {
        const FString& Key = PatternPair.Key;
        const FAccessPattern& Pattern = PatternPair.Value;

        if (Pattern.AccessTimes.Num() > 0)
        {
            const double LastAccessTime = Pattern.AccessTimes.Last();
            const double TimeSinceLastAccess = CurrentTime - LastAccessTime;

            // 【过期检查】超过超时时间的模式标记为删除
            if (TimeSinceLastAccess > PREDICTION_TIMEOUT * 2)  // 双倍超时时间后清理
            {
                KeysToRemove.Add(Key);
            }
        }
    }

    // 【执行清理】
    for (const FString& Key : KeysToRemove)
    {
        AccessPatterns.Remove(Key);
        UE_LOG(LogTemp, VeryVerbose, TEXT("【高级预测】清理过期模式: %s"), *Key);
    }

    UE_LOG(LogTemp, Log, TEXT("【高级预测】清理完成，清理了%d个过期访问模式"), KeysToRemove.Num());
}

float UCacheManager::CalculateSequenceWeight(const TArray<FString>& Pattern, int32 PatternLength) const
{
    // 【权重计算】较长的序列匹配权重更高
    return static_cast<float>(PatternLength) * 1.5f;
}

float UCacheManager::CalculateTimeDecayWeight(double AccessTime) const
{
    // 【时间衰减】考虑时间因素，最近的访问权重更高
    const double CurrentTime = FPlatformTime::Seconds();
    const double TimeDiff = CurrentTime - AccessTime;
    return FMath::Exp(-TimeDiff / 300.0f);  // 5分钟衰减
}

// ========== 【新增】自适应压缩算法选择实现 ==========

ECompressionAlgorithm UCacheManager::SelectOptimalCompressionAlgorithm(const TArray<uint8>& RawData)
{
    if (RawData.Num() == 0)
    {
        return ECompressionAlgorithm::None;
    }

    // 【数据大小分析】根据数据大小进行初步筛选
    const int32 DataSize = RawData.Num();

    // 【小数据优化】小于1KB的数据不压缩，避免压缩开销
    if (DataSize < 1024)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】数据过小(%d字节)，不压缩"), DataSize);
        return ECompressionAlgorithm::None;
    }

    // 【数据熵分析】计算数据的随机性
    float DataEntropy = CalculateDataEntropy(RawData);

    // 【重复模式检测】检查数据中的重复模式
    bool bHasRepeatingPatterns = HasRepeatingPatterns(RawData);

    // 【算法选择逻辑】基于数据特征选择最优算法
    if (DataEntropy < 0.3f && bHasRepeatingPatterns)
    {
        // 【高重复性数据】使用高压缩比算法
        if (DataSize > 100 * 1024) // 大于100KB
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】高重复性大数据，选择Oodle算法"));
            return ECompressionAlgorithm::ZSTD; // 使用Oodle（映射到ZSTD枚举）
        }
        else
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】高重复性中等数据，选择Zlib算法"));
            return ECompressionAlgorithm::Zlib;
        }
    }
    else if (DataEntropy > 0.7f)
    {
        // 【高随机性数据】压缩效果差，优先速度
        if (DataSize > 50 * 1024) // 大于50KB才压缩
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】高随机性数据，选择LZ4快速算法"));
            return ECompressionAlgorithm::LZ4;
        }
        else
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】高随机性小数据，不压缩"));
            return ECompressionAlgorithm::None;
        }
    }
    else
    {
        // 【中等随机性数据】平衡压缩比和速度
        if (DataSize > 200 * 1024) // 大于200KB
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】中等随机性大数据，选择Oodle算法"));
            return ECompressionAlgorithm::ZSTD; // 使用Oodle
        }
        else if (DataSize > 10 * 1024) // 大于10KB
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】中等随机性中等数据，选择Zlib算法"));
            return ECompressionAlgorithm::Zlib;
        }
        else
        {
            UE_LOG(LogTemp, VeryVerbose, TEXT("【自适应压缩】中等随机性小数据，选择LZ4算法"));
            return ECompressionAlgorithm::LZ4;
        }
    }
}

float UCacheManager::CalculateDataEntropy(const TArray<uint8>& Data) const
{
    if (Data.Num() == 0)
    {
        return 0.0f;
    }

    // 【频率统计】统计每个字节值的出现频率
    TMap<uint8, int32> ByteFrequency;
    for (const uint8 Byte : Data)
    {
        ByteFrequency.FindOrAdd(Byte, 0)++;
    }

    // 【香农熵计算】H = -Σ(p * log2(p))
    float Entropy = 0.0f;
    const float TotalCount = static_cast<float>(Data.Num());

    for (const auto& FreqPair : ByteFrequency)
    {
        const float Probability = static_cast<float>(FreqPair.Value) / TotalCount;
        if (Probability > 0.0f)
        {
            Entropy -= Probability * FMath::Log2(Probability);
        }
    }

    // 【归一化】将熵值归一化到0.0-1.0范围
    const float MaxEntropy = FMath::Log2(256.0f); // 8位字节的最大熵
    return FMath::Clamp(Entropy / MaxEntropy, 0.0f, 1.0f);
}

bool UCacheManager::HasRepeatingPatterns(const TArray<uint8>& Data) const
{
    if (Data.Num() < 8)
    {
        return false;
    }

    // 【简化模式检测】检查连续重复的字节序列
    const int32 MaxSamples = FMath::Min(Data.Num(), 1000); // 限制分析范围
    int32 RepeatingCount = 0;

    for (int32 i = 0; i < MaxSamples - 4; ++i)
    {
        // 【检查4字节重复模式】
        bool bFoundRepeat = false;
        for (int32 j = i + 4; j < MaxSamples - 4; ++j)
        {
            if (Data[i] == Data[j] && Data[i+1] == Data[j+1] &&
                Data[i+2] == Data[j+2] && Data[i+3] == Data[j+3])
            {
                RepeatingCount++;
                bFoundRepeat = true;
                break;
            }
        }

        if (bFoundRepeat)
        {
            i += 3; // 跳过已检查的字节
        }
    }

    // 【阈值判断】如果重复模式超过10%，认为有重复模式
    const float RepeatRatio = static_cast<float>(RepeatingCount) / (MaxSamples / 4);
    return RepeatRatio > 0.1f;
}

// ========== 【新增】数据序列化和反序列化实现 ==========

bool UCacheManager::SerializeMapCellData(const TArray<FMapCell>& Data, TArray<uint8>& SerializedData)
{
    if (Data.Num() == 0)
    {
        return false;
    }

    // 【序列化格式】版本号(4字节) + 数据数量(4字节) + 校验和(4字节) + 数据内容
    const uint32 Version = 1; // 序列化版本号
    const uint32 DataCount = Data.Num();
    const int32 DataSize = Data.Num() * sizeof(FMapCell);

    // 【计算总大小】头部信息 + 数据内容
    const int32 HeaderSize = sizeof(uint32) * 3; // 版本号 + 数据数量 + 校验和
    const int32 TotalSize = HeaderSize + DataSize;

    SerializedData.SetNumUninitialized(TotalSize);

    // 【写入头部信息】
    uint8* WritePtr = SerializedData.GetData();

    // 写入版本号
    FMemory::Memcpy(WritePtr, &Version, sizeof(uint32));
    WritePtr += sizeof(uint32);

    // 写入数据数量
    FMemory::Memcpy(WritePtr, &DataCount, sizeof(uint32));
    WritePtr += sizeof(uint32);

    // 【写入数据内容】
    FMemory::Memcpy(WritePtr + sizeof(uint32), Data.GetData(), DataSize);

    // 【计算并写入校验和】
    const uint32 Checksum = CalculateDataChecksum(TArray<uint8>(
        reinterpret_cast<const uint8*>(Data.GetData()), DataSize));
    FMemory::Memcpy(WritePtr, &Checksum, sizeof(uint32));

    UE_LOG(LogTemp, VeryVerbose, TEXT("【数据序列化】成功序列化%d个FMapCell，总大小%d字节，校验和0x%08X"),
           DataCount, TotalSize, Checksum);

    return true;
}

bool UCacheManager::DeserializeMapCellData(const TArray<uint8>& SerializedData, TArray<FMapCell>& Data)
{
    if (SerializedData.Num() < sizeof(uint32) * 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("【数据反序列化】数据太小，无法包含完整头部信息"));
        return false;
    }

    const uint8* ReadPtr = SerializedData.GetData();

    // 【读取版本号】
    uint32 Version = 0;
    FMemory::Memcpy(&Version, ReadPtr, sizeof(uint32));
    ReadPtr += sizeof(uint32);

    if (Version != 1)
    {
        UE_LOG(LogTemp, Warning, TEXT("【数据反序列化】不支持的版本号: %d"), Version);
        return false;
    }

    // 【读取数据数量】
    uint32 DataCount = 0;
    FMemory::Memcpy(&DataCount, ReadPtr, sizeof(uint32));
    ReadPtr += sizeof(uint32);

    // 【读取校验和】
    uint32 StoredChecksum = 0;
    FMemory::Memcpy(&StoredChecksum, ReadPtr, sizeof(uint32));
    ReadPtr += sizeof(uint32);

    // 【验证数据大小】
    const int32 ExpectedDataSize = DataCount * sizeof(FMapCell);
    const int32 HeaderSize = sizeof(uint32) * 3;

    if (SerializedData.Num() != HeaderSize + ExpectedDataSize)
    {
        UE_LOG(LogTemp, Warning, TEXT("【数据反序列化】数据大小不匹配，期望%d，实际%d"),
               HeaderSize + ExpectedDataSize, SerializedData.Num());
        return false;
    }

    // 【验证校验和】
    const uint32 CalculatedChecksum = CalculateDataChecksum(TArray<uint8>(ReadPtr, ExpectedDataSize));
    if (StoredChecksum != CalculatedChecksum)
    {
        UE_LOG(LogTemp, Error, TEXT("【数据反序列化】校验和不匹配，存储0x%08X，计算0x%08X"),
               StoredChecksum, CalculatedChecksum);
        return false;
    }

    // 【读取数据内容】
    Data.SetNumUninitialized(DataCount);
    FMemory::Memcpy(Data.GetData(), ReadPtr, ExpectedDataSize);

    UE_LOG(LogTemp, VeryVerbose, TEXT("【数据反序列化】成功反序列化%d个FMapCell，校验和验证通过"), DataCount);

    return true;
}

uint32 UCacheManager::CalculateDataChecksum(const TArray<uint8>& Data) const
{
    if (Data.Num() == 0)
    {
        return 0;
    }

    // 【CRC32校验】使用简单的CRC32算法计算校验和
    uint32 Checksum = 0xFFFFFFFF;

    for (const uint8 Byte : Data)
    {
        Checksum ^= Byte;
        for (int32 i = 0; i < 8; ++i)
        {
            if (Checksum & 1)
            {
                Checksum = (Checksum >> 1) ^ 0xEDB88320;
            }
            else
            {
                Checksum >>= 1;
            }
        }
    }

    return ~Checksum;
}
